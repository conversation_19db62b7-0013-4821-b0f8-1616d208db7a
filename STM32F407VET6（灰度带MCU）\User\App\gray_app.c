#include "gray_app.h"
#include "No_Mcu_Ganv_Grayscale_Sensor_Config.h" // 使用无MCU传感器驱动

extern UART_HandleTypeDef huart1;

unsigned char Digtal;

float gray_weights[8] = {-4.0f, -3.0f, -2.0f, -1.0f, 1.0f, 2.0f, 3.0f, 4.0f};

float g_line_position_error;

// 无MCU传感器实例
No_MCU_Sensor gray_sensor;

// 默认校准数据（可根据实际情况调整）
unsigned short default_white[8] = {3800, 3800, 3800, 3800, 3800, 3800, 3800, 3800}; // 白色校准值
unsigned short default_black[8] = {200, 200, 200, 200, 200, 200, 200, 200};         // 黑色校准值

void Gray_Init(void)
{
    // 初始化无MCU传感器
    No_MCU_Ganv_Sensor_Init(&gray_sensor, default_white, default_black);

    // 测试ADC功能
    if(Test_ADC_Function()) {
        my_printf(&huart1, "No-MCU Gray Sensor Initialized Successfully!\r\n");
        my_printf(&huart1, "ADC Channel: PB1 (ADC1_IN9), Resolution: 12-bit\r\n");
    } else {
        my_printf(&huart1, "No-MCU Gray Sensor Initialization Failed!\r\n");
    }
}

void Gray_Task(void)
{
//		HAL_NVIC_DisableIRQ(TIM2_IRQn);

		// 执行无MCU传感器任务（数据采集和处理）
		No_Mcu_Ganv_Sensor_Task_Without_tick(&gray_sensor);

		// 获取数字量数据
		uint8_t temp = Get_Digtal_For_User(&gray_sensor);

		// 保持与原有接口的兼容性（取反逻辑）
		Digtal = ~temp;

//		HAL_NVIC_EnableIRQ(TIM2_IRQn);

//    my_printf(&huart1, "Digtal %d-%d-%d-%d-%d-%d-%d-%d\r\n",(Digtal>>0)&0x01,(Digtal>>1)&0x01,(Digtal>>2)&0x01,(Digtal>>3)&0x01,(Digtal>>4)&0x01,(Digtal>>5)&0x01,(Digtal>>6)&0x01,(Digtal>>7)&0x01);

    // 计算线位置误差（保持原有算法）
    float weighted_sum = 0;
    uint8_t black_line_count = 0;

    for(uint8_t i = 0; i < 8; i++)
    {
      if((Digtal>>i) & 0x01)
      {
        weighted_sum += gray_weights[i];
        black_line_count++;
      }
    }

    if(black_line_count > 0)
      g_line_position_error = weighted_sum / (float)black_line_count;
}

/* 函数功能：获取原始ADC值（用于调试和校准）
   参数说明：result - 存储8个通道ADC值的数组
   返回值：1-成功 0-失败 */
uint8_t Gray_Get_Raw_Values(unsigned short *result)
{
    return Get_Anolog_Value(&gray_sensor, result);
}

/* 函数功能：获取归一化值（用于高精度应用）
   参数说明：result - 存储8个通道归一化值的数组
   返回值：1-成功 0-失败 */
uint8_t Gray_Get_Normalized_Values(unsigned short *result)
{
    return Get_Normalize_For_User(&gray_sensor, result);
}

/* 函数功能：重新校准传感器
   参数说明：
   white_values - 白色校准值数组
   black_values - 黑色校准值数组 */
void Gray_Recalibrate(unsigned short *white_values, unsigned short *black_values)
{
    No_MCU_Ganv_Sensor_Init(&gray_sensor, white_values, black_values);
    my_printf(&huart1, "Gray Sensor Recalibrated!\r\n");
}

/* 函数功能：调试输出传感器状态（用于测试和调试）*/
void Gray_Debug_Print(void)
{
    unsigned short raw_values[8];
    unsigned short norm_values[8];

    // 获取原始ADC值
    if(Gray_Get_Raw_Values(raw_values)) {
        my_printf(&huart1, "Raw ADC: %d-%d-%d-%d-%d-%d-%d-%d\r\n",
                  raw_values[0], raw_values[1], raw_values[2], raw_values[3],
                  raw_values[4], raw_values[5], raw_values[6], raw_values[7]);
    }

    // 获取归一化值
    if(Gray_Get_Normalized_Values(norm_values)) {
        my_printf(&huart1, "Normalized: %d-%d-%d-%d-%d-%d-%d-%d\r\n",
                  norm_values[0], norm_values[1], norm_values[2], norm_values[3],
                  norm_values[4], norm_values[5], norm_values[6], norm_values[7]);
    }

    // 输出数字量和误差值
    my_printf(&huart1, "Digital: %d-%d-%d-%d-%d-%d-%d-%d, Error: %.2f\r\n",
              (Digtal>>0)&0x01, (Digtal>>1)&0x01, (Digtal>>2)&0x01, (Digtal>>3)&0x01,
              (Digtal>>4)&0x01, (Digtal>>5)&0x01, (Digtal>>6)&0x01, (Digtal>>7)&0x01,
              g_line_position_error);
}

/* 函数功能：传感器功能测试（用于验证硬件迁移是否成功）*/
void Gray_Hardware_Migration_Test(void)
{
    my_printf(&huart1, "\r\n=== 无MCU灰度传感器硬件迁移测试 ===\r\n");

    // 1. ADC功能测试
    my_printf(&huart1, "1. ADC功能测试...\r\n");
    if(Test_ADC_Function()) {
        uint16_t adc_single = adc_getValue();
        uint16_t adc_avg = Get_ADC_Average(5);
        my_printf(&huart1, "   ADC单次采样: %d, 5次平均: %d\r\n", adc_single, adc_avg);
    } else {
        my_printf(&huart1, "   ADC功能异常!\r\n");
        return;
    }

    // 2. 地址线控制测试
    my_printf(&huart1, "2. 地址线控制测试...\r\n");
    for(uint8_t i = 0; i < 8; i++) {
        // 设置地址线
        HAL_GPIO_WritePin(GRAY_AD0_GPIO_Port, GRAY_AD0_Pin, (i&0x01) ? GPIO_PIN_SET : GPIO_PIN_RESET);
        HAL_GPIO_WritePin(GRAY_AD1_GPIO_Port, GRAY_AD1_Pin, (i&0x02) ? GPIO_PIN_SET : GPIO_PIN_RESET);
        HAL_GPIO_WritePin(GRAY_AD2_GPIO_Port, GRAY_AD2_Pin, (i&0x04) ? GPIO_PIN_SET : GPIO_PIN_RESET);
        HAL_Delay(10);
        uint16_t adc_val = adc_getValue();
        my_printf(&huart1, "   通道%d ADC值: %d\r\n", i, adc_val);
    }

    // 3. 传感器驱动测试
    my_printf(&huart1, "3. 传感器驱动测试...\r\n");
    Gray_Task(); // 执行一次传感器任务
    Gray_Debug_Print(); // 输出调试信息

    my_printf(&huart1, "=== 硬件迁移测试完成 ===\r\n\r\n");
}
