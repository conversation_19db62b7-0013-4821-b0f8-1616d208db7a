#ifndef __GRAY_APP_H
#define __GRAY_APP_H

#include "mydefine.h"

// 基础接口函数（保持兼容性）
void Gray_Init(void);
void Gray_Task(void);

// 扩展接口函数（新增功能）
uint8_t Gray_Get_Raw_Values(unsigned short *result);        // 获取原始ADC值
uint8_t Gray_Get_Normalized_Values(unsigned short *result); // 获取归一化值
void Gray_Recalibrate(unsigned short *white_values, unsigned short *black_values); // 重新校准
void Gray_Debug_Print(void);                                // 调试输出
void Gray_Hardware_Migration_Test(void);                    // 硬件迁移测试

// 全局变量（保持兼容性）
extern unsigned char Digtal;                                // 数字量输出
extern float g_line_position_error;                         // 循迹误差值

#endif
